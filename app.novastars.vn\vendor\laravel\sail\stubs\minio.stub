minio:
    image: 'minio/minio:latest'
    ports:
        - '${FORWARD_MINIO_PORT:-9000}:9000'
        - '${FORWARD_MINIO_CONSOLE_PORT:-8900}:8900'
    environment:
        MINIO_ROOT_USER: 'sail'
        MINIO_ROOT_PASSWORD: 'password'
    volumes:
        - 'sail-minio:/data/minio'
    networks:
        - sail
    command: minio server /data/minio --console-address ":8900"
    healthcheck:
        test: ["CMD", "mc", "ready", "local"]
        retries: 3
        timeout: 5s
